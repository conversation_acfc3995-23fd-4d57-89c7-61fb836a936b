// MSW Server Setup for API Mocking
// This file sets up a mock server for testing API calls

let setupServer, http, HttpResponse

try {
  // Try to import MSW v2 syntax
  const mswNode = require('msw/node')
  const msw = require('msw')

  setupServer = mswNode.setupServer
  http = msw.http
  HttpResponse = msw.HttpResponse
} catch (error) {
  console.warn('MSW not available, using mock implementations')

  // Fallback mock implementations
  setupServer = (...handlers) => ({
    listen: () => {},
    resetHandlers: () => {},
    close: () => {},
  })

  http = {
    get: () => {},
    post: () => {},
    put: () => {},
    delete: () => {},
  }

  HttpResponse = {
    json: (data) => ({ data }),
  }
}

// Mock API responses
const handlers = [
  // Auth endpoints
  http.post('*/api/v1/users/login/', () => {
    return HttpResponse.json({
      user: {
        id: 1,
        email: '<EMAIL>',
        name: 'Test User',
      },
      access: 'mock-access-token',
      refresh: 'mock-refresh-token',
    })
  }),

  http.post('*/api/v1/users/register/', () => {
    return HttpResponse.json({
      user: {
        id: 1,
        email: '<EMAIL>',
        name: 'Test User',
      },
      access: 'mock-access-token',
      refresh: 'mock-refresh-token',
    })
  }),

  http.post('*/api/v1/users/forgot-password/', () => {
    return HttpResponse.json({
      message: 'Password reset email sent successfully',
    })
  }),

  http.post('*/api/v1/users/reset-password/', () => {
    return HttpResponse.json({
      message: 'Password reset successfully',
    })
  }),

  // Product endpoints
  http.get('*/api/v1/products/', () => {
    return HttpResponse.json({
      results: [
        {
          id: 1,
          name: 'Test Product 1',
          slug: 'test-product-1',
          price: 99.99,
          images: [{ image: '/test-image-1.jpg' }],
          brand: { name: 'Test Brand' },
          category: { name: 'Test Category' },
          description: 'Test product 1 description',
          stock: 10,
        },
        {
          id: 2,
          name: 'Test Product 2',
          slug: 'test-product-2',
          price: 149.99,
          images: [{ image: '/test-image-2.jpg' }],
          brand: { name: 'Test Brand' },
          category: { name: 'Test Category' },
          description: 'Test product 2 description',
          stock: 5,
        },
      ],
      count: 2,
      next: null,
      previous: null,
    })
  }),

  http.get('*/api/v1/products/:slug/', ({ params }) => {
    return HttpResponse.json({
      id: 1,
      name: 'Test Product',
      slug: params.slug,
      price: 99.99,
      images: [{ image: '/test-image.jpg' }],
      brand: { name: 'Test Brand' },
      category: { name: 'Test Category' },
      description: 'Test product description',
      stock: 10,
      specifications: [],
      reviews: [],
    })
  }),

  // Cart endpoints
  http.get('*/api/v1/cart/', () => {
    return HttpResponse.json({
      items: [
        {
          id: 1,
          product: {
            id: 1,
            name: 'Test Product',
            slug: 'test-product',
            price: 99.99,
            images: [{ image: '/test-image.jpg' }],
          },
          quantity: 2,
          price: 99.99,
        },
      ],
      total: 199.98,
      count: 1,
    })
  }),

  http.post('*/api/v1/cart/add/', () => {
    return HttpResponse.json({
      message: 'Product added to cart',
      item: {
        id: 1,
        product: {
          id: 1,
          name: 'Test Product',
          slug: 'test-product',
          price: 99.99,
        },
        quantity: 1,
        price: 99.99,
      },
    })
  }),

  http.put('*/api/v1/cart/update/', () => {
    return HttpResponse.json({
      message: 'Cart updated successfully',
    })
  }),

  http.delete('*/api/v1/cart/remove/:id/', () => {
    return HttpResponse.json({
      message: 'Item removed from cart',
    })
  }),

  // Categories endpoint
  http.get('*/api/v1/categories/', () => {
    return HttpResponse.json([
      {
        id: 1,
        name: 'Electronics',
        slug: 'electronics',
        image: '/category-electronics.jpg',
      },
      {
        id: 2,
        name: 'Appliances',
        slug: 'appliances',
        image: '/category-appliances.jpg',
      },
    ])
  }),

  // Orders endpoint
  http.post('*/api/v1/orders/', () => {
    return HttpResponse.json({
      id: 1,
      order_number: 'ORD-001',
      status: 'pending',
      total: 199.98,
      items: [
        {
          product: {
            name: 'Test Product',
            price: 99.99,
          },
          quantity: 2,
          price: 99.99,
        },
      ],
    })
  }),

  // Shipping endpoints
  http.post('*/api/v1/shipping/validate-pincode/', () => {
    return HttpResponse.json({
      valid: true,
      serviceable: true,
      city: 'Test City',
      state: 'Test State',
    })
  }),

  http.post('*/api/v1/shipping/calculate-rates/', () => {
    return HttpResponse.json({
      rates: [
        {
          courier_name: 'Test Courier',
          rate: 50,
          estimated_delivery_days: 3,
          cod_available: true,
        },
      ],
    })
  }),

  // Payment endpoints
  http.post('*/api/v1/payments/phonepe/initiate/', () => {
    return HttpResponse.json({
      payment_url: 'https://test-payment-url.com',
      transaction_id: 'TXN-123',
    })
  }),
]

const server = setupServer(...handlers)

module.exports = { server }
